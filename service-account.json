{"type": "service_account", "project_id": "prepwise-59573", "private_key_id": "262b97f0fdf34c606bc777ee07f32bb694c1f03c", "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCX+gboMj4wpISp\nBiOexZoXxsptGbccI9YyDt0cuNTflbitcOYA5kOiskFB3btdQ36QqPznp4/D27l/\na9qy2JTUNkJTtP7DjCTS7f2qttMPUOXyXgnuRV7dfn6L3mrPm+qRsmoWh2N3ZbLG\nMOHe7ZLvoufDnD4cOmp0h604GWDhChqodtNKfT3KkEmNu5k5FSyvfR3yzyNmNxtO\nxwfpoJ2arc3Qx0ztbr7Wi+Z4/JUnNYT6HyRo199l3A51ukLPq39ZioUyLamEcz6s\nj3AM/7s5GF3r9eB1iaQJhgp+X4dzHEP6jKWE7rB8bMgVE9J6/ShzB1+tRdy8+8LH\nvZTliNFPAgMBAAECggEACKPXGFn24tFk61kP1bBv4hHT+J+MI0Y+9f9AqdKGOPnW\n6HaxKh7tJxw8N9rmZ9cfpGi/DAp2KBE0pV5ZLhlrCKTXC3w65EdyNn2qPLYtH0NJ\noghKRz4GtnqWW4GhupDSJUX/4FetG3Ya051RfK4upX9c7r18uHfHt+fuEfDmFZUP\niQxLehvErJLetOShAk1+p6Px8WSx5J+lr+n6TbXP0qxXVpiM285J80+/FmX+TaiJ\n97mrgGyzgifxbEtLyWAeS0w6E/B+IjOE1sY68WZ68frvc9CnT0pUcJAf+P5REZ2T\nTh8vzIjcPSblcs9snPMg038eQXMdN2VpRbtPaHXgGQKBgQDLcxlm7xWnJfcv2cNF\nsBktUtadXzoIxUqL1haBfFQa/Lr/aiZpy12PTdBTQJJnJslCUjK4wnuzvBzv+LxH\nZRJrWtGVE9Sbn0l4fzHXsPwTPcK7G5U++9AjXjIg9GD3SJKY0TXNDg8nrN73qIjL\n9ILzQ+f/lW91ydmmYCVCWUR9VwKBgQC/O1TYJVv39TbQrl67qFO7H8dFXCuUufFn\n77ek/V/rGin0zWcPVahlyOfAMTTHUP7dwseK03QwafETrz/ecY47tv/vUSRoB1Xt\nu9R1g8eI2P/bWynNsaIRfkXY5zmF5hTvyecA4nkDN0AKIx/SM0Pczb3/KYzzp5El\nM3wSwWfYyQKBgQCMgYAgm6u7lTfCn0UCAt1QdO5tHxAIcKDMNF20rXW0cah/rwK6\ne82wo4/lfync/WdvWy9WdFJUTe06OfFl6UKwS7eEmn5xv69Q/Z8hlERy0WuEJ0eK\niQQ0+N/r7HpvLXq4+XuLk1MaFoSiS422NbaeZN57n+urUGz1P19cFCR93wKBgFPf\nae8DM7slSc1tLQ+RXGgOIBBDxLmOdFrpwNoUZyVubEh6bmFDaIB8BWk+K6/fzmjl\nLS+uXp99V8cSbijaFVALi2UjaZxW21FRuHVyPum+gzlqWoNlDXgusePYRlp792LU\nYwT4T6shaG0JysMeyPznIBXx22hibj6qvhhby3hBAoGBAMhf2t0ZdJll/5tt/6vJ\nF7bVuguMo1TeYXbPCIwptiRkiO+f1lE+3OjxdpKWWnwNJc/0InX5R7JGG8DosfQZ\nXIhWCY5v9Q+sGPxNtlfIh32Y2vix6xEyEz9QxQY6jGRp1XOmiKXys93YSw/Tw2Os\nmIlaLgjODItEdFQxQFfoy+Cn\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "114778028190083288074", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40prepwise-59573.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}