{"name": "interview-prep", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.5", "@radix-ui/react-slot": "^1.2.1", "@vapi-ai/web": "^2.3.6", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^11.4.0", "firebase-admin": "^11.11.1", "lucide-react": "^0.507.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}