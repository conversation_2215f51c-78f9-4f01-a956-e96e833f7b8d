// test-env.js - Run this with: node test-env.js
import dotenv from "dotenv";
dotenv.config({ path: ".env.local" });

console.log("Environment Variables Check:");
console.log(
  "FIREBASE_PROJECT_ID:",
  process.env.FIREBASE_PROJECT_ID ? "✓ Set" : "✗ Missing"
);
console.log(
  "FIREBASE_CLIENT_EMAIL:",
  process.env.FIREBASE_CLIENT_EMAIL ? "✓ Set" : "✗ Missing"
);
console.log(
  "FIREBASE_PRIVATE_KEY:",
  process.env.FIREBASE_PRIVATE_KEY ? "✓ Set" : "✗ Missing"
);

if (process.env.FIREBASE_PRIVATE_KEY) {
  const key = process.env.FIREBASE_PRIVATE_KEY;
  console.log("\nPrivate Key Analysis:");
  console.log("Length:", key.length);
  console.log("Starts with:", key.substring(0, 30));
  console.log("Contains \\n:", key.includes("\\n"));
  console.log("Contains actual newlines:", key.includes("\n"));
  console.log("Ends with:", key.substring(key.length - 30));
}
