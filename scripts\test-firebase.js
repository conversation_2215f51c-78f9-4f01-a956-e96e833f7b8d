// Test script to verify Firebase Admin SDK setup
const { auth, db } = require('../firebase/admin.ts');

async function testFirebaseConnection() {
  try {
    console.log('Testing Firebase Admin SDK connection...');
    
    // Test Firestore connection
    console.log('Testing Firestore connection...');
    const testDoc = await db.collection('test').doc('connection').set({
      timestamp: new Date().toISOString(),
      test: true
    });
    console.log('✅ Firestore connection successful');
    
    // Clean up test document
    await db.collection('test').doc('connection').delete();
    console.log('✅ Test document cleaned up');
    
    // Test Auth connection
    console.log('Testing Firebase Auth connection...');
    const users = await auth.listUsers(1);
    console.log('✅ Firebase Auth connection successful');
    console.log(`Found ${users.users.length} users in the first page`);
    
    console.log('🎉 All Firebase connections are working properly!');
    
  } catch (error) {
    console.error('❌ Firebase connection test failed:', error);
    process.exit(1);
  }
}

testFirebaseConnection();
