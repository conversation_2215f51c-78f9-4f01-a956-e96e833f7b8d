// Test Firestore REST API approach
import { initializeApp, cert } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import dotenv from "dotenv";

dotenv.config({ path: ".env.local" });

async function testFirestoreRestAPI() {
  try {
    console.log("🔧 Initializing Firebase Admin for Auth...");
    
    const app = initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      }),
    });

    const auth = getAuth(app);
    console.log("✅ Firebase Admin Auth initialized");

    console.log("🔑 Getting access token...");
    const accessToken = await auth.app.options.credential?.getAccessToken();
    console.log("✅ Access token obtained");

    const FIRESTORE_BASE_URL = `https://firestore.googleapis.com/v1/projects/prepwise-59573/databases/(default)/documents`;

    // Test write operation
    console.log("📝 Testing Firestore REST API write...");
    const testData = {
      fields: {
        name: { stringValue: "Test User" },
        email: { stringValue: "<EMAIL>" },
        createdAt: { stringValue: new Date().toISOString() },
      }
    };

    const writeResponse = await fetch(`${FIRESTORE_BASE_URL}/test/rest-api-test`, {
      method: "PATCH",
      headers: {
        "Authorization": `Bearer ${accessToken.access_token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testData),
    });

    if (!writeResponse.ok) {
      throw new Error(`Write failed: ${writeResponse.status} ${await writeResponse.text()}`);
    }

    console.log("✅ Firestore REST API write successful");

    // Test read operation
    console.log("🔍 Testing Firestore REST API read...");
    const readResponse = await fetch(`${FIRESTORE_BASE_URL}/test/rest-api-test`, {
      headers: {
        "Authorization": `Bearer ${accessToken.access_token}`,
      },
    });

    if (!readResponse.ok) {
      throw new Error(`Read failed: ${readResponse.status} ${await readResponse.text()}`);
    }

    const readData = await readResponse.json();
    console.log("✅ Firestore REST API read successful:", readData);

    // Clean up
    console.log("🧹 Cleaning up test document...");
    const deleteResponse = await fetch(`${FIRESTORE_BASE_URL}/test/rest-api-test`, {
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${accessToken.access_token}`,
      },
    });

    if (!deleteResponse.ok) {
      console.log("⚠️ Delete failed, but that's okay for testing");
    } else {
      console.log("✅ Test document deleted");
    }

    console.log("🎉 Firestore REST API test completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Firestore REST API test failed:");
    console.error("Error message:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  }
}

testFirestoreRestAPI();
