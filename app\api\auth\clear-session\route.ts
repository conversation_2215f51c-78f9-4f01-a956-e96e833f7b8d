import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    cookieStore.delete("session");
    
    return NextResponse.json({ success: true, message: "Session cleared" });
  } catch (error) {
    console.error("Error clearing session:", error);
    return NextResponse.json(
      { success: false, message: "Failed to clear session" },
      { status: 500 }
    );
  }
}
