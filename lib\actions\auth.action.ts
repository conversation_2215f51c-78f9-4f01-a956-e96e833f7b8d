"use server";
import { auth, db } from "@/firebase/admin";
import { cookies } from "next/headers";

// Session duration
const ONE_WEEK = 60 * 60 * 24 * 7;

// Set session cookie
export async function setSessionCookie(idToken: string) {
  const cookieStore = await cookies();

  // Create session cookie
  const sessionCookie = await auth.createSessionCookie(idToken, {
    expiresIn: ONE_WEEK * 1000,
  });

  // Set cookie in the browser
  cookieStore.set("session", sessionCookie, {
    maxAge: ONE_WEEK,
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    path: "/",
    sameSite: "lax",
  });
}

// Helper function to retry Firestore operations
async function retryFirestoreOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      console.error(`Firestore operation attempt ${attempt} failed:`, error);

      if (attempt === maxRetries) {
        throw error;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delay * attempt));
    }
  }
  throw new Error("Max retries exceeded");
}

export async function signUp(params: SignUpParams) {
  const { uid, name, email } = params;

  if (!uid || typeof uid !== "string") {
    console.error("Invalid UID:", uid);
    return {
      success: false,
      message: "Invalid UID provided.",
    };
  }

  if (!name || !email) {
    console.error("Missing required fields:", { name, email });
    return {
      success: false,
      message: "Name and email are required.",
    };
  }

  try {
    console.log("Checking if user exists for UID:", uid);
    const userRecord = await retryFirestoreOperation(async () => {
      return await db.collection("users").doc(uid).get();
    });

    if (userRecord.exists) {
      console.log("User already exists in Firestore");
      return {
        success: false,
        message: "User already exists. Please sign in instead.",
      };
    }

    console.log("Creating user document in Firestore for UID:", uid);
    // save user to db with additional metadata
    await retryFirestoreOperation(async () => {
      return await db.collection("users").doc(uid).set({
        name,
        email,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    });

    console.log("User document created successfully in Firestore");
    return {
      success: true,
      message: "Account created successfully",
    };
  } catch (e: unknown) {
    console.error("Error creating a user:", e);

    const error = e as any;

    // Handle specific Firebase errors
    if (error?.code === "auth/email-already-exists") {
      return {
        success: false,
        message: "This email is already in use.",
      };
    }

    if (error?.code === "permission-denied") {
      return {
        success: false,
        message: "Database permission denied. Please contact support.",
      };
    }

    if (
      error?.message?.includes("buffer bounds") ||
      error?.code === "ERR_BUFFER_OUT_OF_BOUNDS"
    ) {
      return {
        success: false,
        message: "Database connection error. Please try again later.",
      };
    }

    return {
      success: false,
      message: "Failed to create an account. Please try again.",
    };
  }
}

export async function signIn(params: SignInParams) {
  const { email, idToken } = params;

  if (!email || !idToken) {
    console.error("Missing required fields:", {
      email: !!email,
      idToken: !!idToken,
    });
    return {
      success: false,
      message: "Email and ID token are required.",
    };
  }

  try {
    console.log("Attempting to sign in user with email:", email);

    // Verify the ID token first
    const decodedToken = await auth.verifyIdToken(idToken);
    if (!decodedToken || !decodedToken.uid) {
      console.error("Invalid ID token");
      return {
        success: false,
        message: "Invalid authentication token.",
      };
    }

    // Check if user exists in Firebase Auth
    const userRecord = await auth.getUser(decodedToken.uid);
    if (!userRecord) {
      console.error("User not found in Firebase Auth");
      return {
        success: false,
        message: "User not found. Please sign up instead.",
      };
    }

    // Check if user exists in Firestore
    const userDoc = await db.collection("users").doc(decodedToken.uid).get();
    if (!userDoc.exists) {
      console.error(
        "User not found in Firestore, but exists in Auth. Creating user document."
      );
      // Create user document if it doesn't exist
      await db
        .collection("users")
        .doc(decodedToken.uid)
        .set({
          name: userRecord.displayName || "User",
          email: userRecord.email || email,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
    }

    await setSessionCookie(idToken);
    console.log("User signed in successfully");

    // Return success response
    return {
      success: true,
      message: "Signed in successfully",
    };
  } catch (e: unknown) {
    console.error("Error signing in", e);

    const error = e as any;
    if (error?.code === "auth/user-not-found") {
      return {
        success: false,
        message: "User not found. Please sign up instead.",
      };
    }

    if (error?.code === "auth/id-token-expired") {
      return {
        success: false,
        message: "Authentication token expired. Please try again.",
      };
    }

    return {
      success: false,
      message: "Failed to log in. Please try again.",
    };
  }
}

export async function getCurrentUser(): Promise<User | null> {
  const cookieStore = await cookies();

  const sessionCookie = cookieStore.get("session")?.value;
  if (!sessionCookie) return null;

  try {
    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true);
    if (!decodedClaims || !decodedClaims.uid) {
      console.error("Invalid session claims or missing UID");
      return null;
    }

    // get user info from db
    const userRecord = await db
      .collection("users")
      .doc(decodedClaims.uid)
      .get();

    if (!userRecord.exists) {
      console.error(
        "User document not found in Firestore for UID:",
        decodedClaims.uid
      );
      return null;
    }

    const userData = userRecord.data();
    if (!userData) {
      console.error("User document exists but has no data");
      return null;
    }

    return {
      ...userData,
      id: userRecord.id,
    } as User;
  } catch (e) {
    console.error("Error verifying session cookie:", e);
    return null;
  }
}

// Clear session cookie
export async function clearSessionCookie() {
  const cookieStore = await cookies();
  try {
    cookieStore.delete("session");
    console.log("Session cookie cleared");
  } catch (error) {
    console.error("Error clearing session cookie:", error);
  }
}

// Sign out user
export async function signOut() {
  try {
    await clearSessionCookie();
    return {
      success: true,
      message: "Signed out successfully",
    };
  } catch (error) {
    console.error("Error signing out:", error);
    return {
      success: false,
      message: "Failed to sign out",
    };
  }
}

// Check if user is authenticated
export async function isAuthenticated() {
  const user = await getCurrentUser();
  return !!user;
}
