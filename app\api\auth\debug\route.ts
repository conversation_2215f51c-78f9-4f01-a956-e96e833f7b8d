import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { auth } from "@/firebase/admin";

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("session")?.value;
    
    const response = {
      hasSessionCookie: !!sessionCookie,
      sessionCookieLength: sessionCookie?.length || 0,
      sessionCookiePreview: sessionCookie ? sessionCookie.substring(0, 50) + "..." : null,
    };

    if (sessionCookie) {
      try {
        const decodedClaims = await auth.verifySessionCookie(sessionCookie, true);
        response.sessionValid = true;
        response.uid = decodedClaims.uid;
      } catch (error) {
        response.sessionValid = false;
        response.error = error.message;
        
        // Clear invalid session cookie
        cookieStore.delete("session");
        response.clearedInvalidCookie = true;
      }
    }
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in debug route:", error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
