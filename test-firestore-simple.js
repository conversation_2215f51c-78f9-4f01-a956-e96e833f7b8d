// Simple Firestore test with Node.js v22.16.0
import { initializeApp, cert } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import dotenv from "dotenv";

dotenv.config({ path: ".env.local" });

async function testFirestore() {
  try {
    console.log("🔧 Testing with Node.js version:", process.version);
    console.log("🔧 Initializing Firebase Admin...");

    const app = initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      }),
    });

    const db = getFirestore(app);
    console.log("✅ Firebase Admin initialized");

    console.log("🔍 Testing Firestore read operation...");
    const testDoc = await db.collection("_test").doc("connection").get();
    console.log("✅ Firestore read operation successful");

    console.log("📝 Testing Firestore write operation...");
    await db.collection("_test").doc("connection").set({
      timestamp: new Date().toISOString(),
      test: true,
      nodeVersion: process.version,
    });
    console.log("✅ Firestore write operation successful");

    console.log("🔍 Testing Firestore read after write...");
    const verifyDoc = await db.collection("_test").doc("connection").get();
    if (verifyDoc.exists) {
      console.log("✅ Document data:", verifyDoc.data());
    }

    console.log("🧹 Cleaning up test document...");
    await db.collection("_test").doc("connection").delete();
    console.log("✅ Test document deleted");

    console.log("🎉 All Firestore operations completed successfully with Node.js", process.version);
    process.exit(0);
  } catch (error) {
    console.error("❌ Firestore test failed with Node.js", process.version);
    console.error("Error message:", error.message);
    console.error("Error code:", error.code);
    console.error("Full error:", error);
    process.exit(1);
  }
}

testFirestore();
