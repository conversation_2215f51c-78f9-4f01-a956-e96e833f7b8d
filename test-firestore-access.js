// Create this as: test-firestore-access.js
// Run with: node test-firestore-access.js
import dotenv from "dotenv";
dotenv.config({ path: ".env.local" });
import { initializeApp, cert } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";

// const { initializeApp, cert } = require("firebase-admin/app");
// const { getFirestore } = require("firebase-admin/firestore");

const testFirestoreAccess = async () => {
  try {
    console.log("Testing Firestore access...");

    // Initialize Firebase Admin
    const app = initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      }),
    });

    const db = getFirestore(app);

    // Test write operation (this is what was failing)
    console.log("Attempting to write to Firestore...");
    await db.collection("test").doc("permission-check").set({
      message: "Firestore access working!",
      timestamp: new Date().toISOString(),
    });
    console.log("✅ Write operation successful!");

    // Test read operation
    console.log("Attempting to read from Firestore...");
    const doc = await db.collection("test").doc("permission-check").get();
    if (doc.exists) {
      console.log("✅ Read operation successful!");
      console.log("Document data:", doc.data());
    }

    console.log("🎉 All Firestore operations working correctly!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Firestore access test failed:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  }
};

testFirestoreAccess();
