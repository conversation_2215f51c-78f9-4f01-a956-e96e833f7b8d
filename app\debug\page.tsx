import { cookies } from "next/headers";

export default async function DebugPage() {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get("session");

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Authentication</h1>

      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Session Cookie Status:</h2>
          <p>Has Session Cookie: {sessionCookie ? "Yes" : "No"}</p>
          {sessionCookie && (
            <div>
              <p>Cookie Length: {sessionCookie.value.length}</p>
              <p>Cookie Preview: {sessionCookie.value.substring(0, 50)}...</p>
            </div>
          )}
        </div>

        <div>
          <h2 className="text-lg font-semibold">Test Links:</h2>
          <div className="space-y-2">
            <div>
              <a href="/api/auth/debug" className="text-blue-500 underline">
                API Debug Endpoint
              </a>
            </div>
            <div>
              <a href="/sign-up" className="text-blue-500 underline">
                Sign Up Page
              </a>
            </div>
            <div>
              <a href="/sign-in" className="text-blue-500 underline">
                Sign In Page
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
