// Test signup process
import { initializeApp } from "firebase/app";
import { getAuth, createUserWithEmailAndPassword } from "firebase/auth";
import { getFirestore, doc, getDoc, setDoc } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyDlciZya56e8i-Z6Nd5duV0wQREZmKZy7U",
  authDomain: "prepwise-59573.firebaseapp.com",
  projectId: "prepwise-59573",
  storageBucket: "prepwise-59573.firebasestorage.app",
  messagingSenderId: "100081173229",
  appId: "1:100081173229:web:c9ec781d26471bed64c2e4",
  measurementId: "G-VBRT67FMPR",
};

async function testSignup() {
  try {
    console.log("🔧 Initializing Firebase Client...");
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    const db = getFirestore(app);
    console.log("✅ Firebase Client initialized");

    const testEmail = `test${Date.now()}@example.com`;
    const testPassword = "testpassword123";
    const testName = "Test User";

    console.log("👤 Creating user in Firebase Auth...");
    const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
    const uid = userCredential.user.uid;
    console.log("✅ User created in Auth with UID:", uid);

    console.log("📄 Creating user document in Firestore...");
    const userDocRef = doc(db, "users", uid);
    await setDoc(userDocRef, {
      name: testName,
      email: testEmail,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    console.log("✅ User document created in Firestore");

    console.log("🔍 Verifying user document...");
    const userDoc = await getDoc(userDocRef);
    if (userDoc.exists()) {
      console.log("✅ User document verified:", userDoc.data());
    } else {
      console.log("❌ User document not found");
    }

    console.log("🎉 Signup test completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Signup test failed:");
    console.error("Error message:", error.message);
    console.error("Error code:", error.code);
    console.error("Full error:", error);
    process.exit(1);
  }
}

testSignup();
