// debug-private-key.js
// Run with: node debug-private-key.js
import dotenv from "dotenv";

dotenv.config({ path: ".env.local" });

console.log("=== DEBUGGING PRIVATE KEY ===");

const privateKey = process.env.FIREBASE_PRIVATE_KEY;

if (!privateKey) {
  console.log("❌ FIREBASE_PRIVATE_KEY is not set");
  process.exit(1);
}

console.log("Private Key Analysis:");
console.log("Length:", privateKey.length);
console.log("First 50 chars:", privateKey.substring(0, 50));
console.log("Last 50 chars:", privateKey.substring(privateKey.length - 50));
console.log("Contains \\n:", privateKey.includes("\\n"));
console.log("Contains actual newlines:", privateKey.includes("\n"));
console.log(
  "Starts with BEGIN:",
  privateKey.includes("-----BEGIN PRIVATE KEY-----")
);
console.log("Ends with END:", privateKey.includes("-----END PRIVATE KEY-----"));

console.log("\n=== AFTER REPLACEMENT ===");
const processedKey = privateKey.replace(/\\n/g, "\n");
console.log("After \\n replacement:");
console.log("Length:", processedKey.length);
console.log("First 100 chars:");
console.log(processedKey.substring(0, 100));
console.log("Last 100 chars:");
console.log(processedKey.substring(processedKey.length - 100));

// Try to validate the key format
const lines = processedKey.split("\n");
console.log("\nKey structure:");
console.log("Number of lines:", lines.length);
console.log("First line:", lines[0]);
console.log("Last line:", lines[lines.length - 1]);

// Check if it's a valid PEM format
const pemRegex =
  /^-----BEGIN PRIVATE KEY-----[\s\S]*-----END PRIVATE KEY-----$/;
console.log("Valid PEM format:", pemRegex.test(processedKey));

console.log("\n=== RECOMMENDATIONS ===");
if (!processedKey.includes("-----BEGIN PRIVATE KEY-----")) {
  console.log("❌ Missing BEGIN header");
}
if (!processedKey.includes("-----END PRIVATE KEY-----")) {
  console.log("❌ Missing END footer");
}
if (lines.length < 20) {
  console.log("⚠️  Suspiciously few lines - might be malformed");
}
if (lines.length > 50) {
  console.log("⚠️  Too many lines - might have extra content");
}
