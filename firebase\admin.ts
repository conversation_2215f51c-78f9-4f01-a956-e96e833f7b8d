import { getApps, initializeApp, cert } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

// Initialize Firebase Admin SDK
const initFirebaseAdmin = () => {
  try {
    const apps = getApps();

    if (!apps.length) {
      console.log("Initializing Firebase Admin SDK...");

      // Validate environment variables
      const projectId = process.env.FIREBASE_PROJECT_ID;
      const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
      const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(
        /\\n/g,
        "\n"
      );

      if (!projectId || !clientEmail || !privateKey) {
        throw new Error("Missing Firebase Admin SDK environment variables");
      }

      console.log("Firebase Admin SDK config:", {
        projectId,
        clientEmail,
        privateKeyLength: privateKey.length,
      });

      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      });

      console.log("Firebase Admin SDK initialized successfully");
    } else {
      console.log("Firebase Admin SDK already initialized");
    }

    return {
      auth: getAuth(),
      db: getFirestore(),
    };
  } catch (error) {
    console.error("Firebase Admin SDK initialization error:", error);
    throw error;
  }
};

export const { auth, db } = initFirebaseAdmin();
